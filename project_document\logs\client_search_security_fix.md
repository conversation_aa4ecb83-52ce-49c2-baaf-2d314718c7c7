# 客户搜索权限安全漏洞修复记录

## 修复时间
2025-06-06 15:57:37 +08:00

## 问题描述

### 安全漏洞
在客户搜索功能中发现严重的权限安全漏洞：员工在搜索自己的客户时，如果没有找到结果，系统会自动搜索公司所有客户，导致员工可以查看其他员工的客户信息。

### 影响范围
- ✅ **用户端日报提交**：正常（只能搜索自己的客户）
- ❌ **用户端部门日报编辑**：存在漏洞（可能搜索到其他员工客户）
- ❌ **管理端日报编辑**：存在漏洞（可能搜索到其他员工客户）

### 漏洞位置
**文件**: `src/main/java/org/example/company_management/service/impl/ClientServiceImpl.java`
**方法**: `getMyClientsByPage`
**行号**: 第159-161行

## 漏洞分析

### 原始代码逻辑
```java
List<Client> clients = clientMapper.selectMyClientsByPage(params);
// 判断是否有搜索客户名称并且自己的客户查询结果为空则根据提供的客户名称搜索公司所有客户
if (params.containsKey("name") && params.get("name") != null && !params.get("name").toString().isEmpty() && clients.isEmpty()) {
    clients = clientMapper.selectOtherClientsByName(params.get("name").toString());
}
return clients;
```

### 安全风险
1. **数据泄露**：员工可以通过搜索功能查看其他员工的客户信息
2. **权限绕过**：绕过了基于员工ID的数据隔离机制
3. **业务违规**：违反了"员工只能查看自己负责的客户"的业务规则

### 攻击场景
1. 员工A在日报中搜索客户"ABC公司"
2. 如果员工A没有负责"ABC公司"，搜索结果为空
3. 系统自动搜索所有员工的客户，返回其他员工负责的"ABC公司"
4. 员工A获得了不应该看到的客户信息

## 修复方案

### 1. 后端Service层修复

**修复文件**: `src/main/java/org/example/company_management/service/impl/ClientServiceImpl.java`

**修复前**:
```java
List<Client> clients = clientMapper.selectMyClientsByPage(params);
// 判断是否有搜索客户名称并且自己的客户查询结果为空则根据提供的客户名称搜索公司所有客户
if (params.containsKey("name") && params.get("name") != null && !params.get("name").toString().isEmpty() && clients.isEmpty()) {
    clients = clientMapper.selectOtherClientsByName(params.get("name").toString());
}
return clients;
```

**修复后**:
```java
List<Client> clients = clientMapper.selectMyClientsByPage(params);
// 安全修复：删除搜索其他员工客户的逻辑，只能搜索自己负责的客户
// 原逻辑存在安全漏洞：员工可以通过搜索功能查看其他员工的客户信息
// if (params.containsKey("name") && params.get("name") != null && !params.get("name").toString().isEmpty() && clients.isEmpty()) {
//     clients = clientMapper.selectOtherClientsByName(params.get("name").toString());
// }
return clients;
```

### 2. 前端API层修复

**修复文件**: `Company_management_system_admin/src/api/salesReport.js`

**问题**: 管理端存在错误的API别名导出
```javascript
// 错误的别名导出
export { getAllClients as getMyClients }
```

**修复**: 删除错误别名，提供正确的API实现
```javascript
// 获取当前用户的客户列表（管理员自己的客户）
export function getMyClients(params = {}) {
    return request({
        url: '/sales-report/my-clients',
        method: 'get',
        params
    })
}
```

## 验证结果

### 后端API验证

**✅ 正确的API调用流程**:

1. **用户端日报提交**:
   - 调用: `getMyClients()` → `/sales-report/my-clients`
   - 权限: 只返回当前用户的客户

2. **用户端部门日报编辑**:
   - 调用: `getDepartmentEmployeeClients(employeeId)` → `/sales-report/department/employee-clients/{employeeId}`
   - 权限: 只返回指定员工的客户（需要部门权限验证）

3. **管理端日报编辑**:
   - 调用: `getEmployeeClients(employeeId)` → `/sales-report/admin/employee-clients/{employeeId}`
   - 权限: 只返回指定员工的客户（需要管理员权限验证）

### 前端页面验证

**✅ 用户端页面逻辑**:
- `SalesReportBasic.vue`: 只调用 `getMyClients()` - 正确
- `DepartmentReportsBasic.vue`: 根据情况调用 `getDepartmentEmployeeClients(employeeId)` 或 `getMyClients()` - 正确

**✅ 管理端页面逻辑**:
- `SalesReportManagementBasic.vue`: 根据情况调用 `getEmployeeClients(employeeId)` 或 `getMyClients()` - 正确

## 安全加固效果

### 修复前的风险
- ❌ 员工可以通过搜索查看其他员工的客户
- ❌ 数据隔离机制被绕过
- ❌ 存在数据泄露风险

### 修复后的安全保障
- ✅ 员工只能搜索自己负责的客户
- ✅ 部门负责人只能搜索本部门员工的客户
- ✅ 管理员可以搜索指定员工的客户（有权限验证）
- ✅ 数据隔离机制得到严格执行

## 业务影响分析

### 正面影响
1. **数据安全**：消除了客户信息泄露的风险
2. **权限合规**：严格执行了基于角色的访问控制
3. **业务规范**：确保员工只能访问自己负责的客户

### 可能的用户体验变化
1. **搜索结果减少**：员工搜索时不再显示其他员工的客户
2. **更精确的结果**：搜索结果更符合用户的实际权限范围

### 业务流程优化建议
1. **客户分配透明化**：如果需要查看其他员工的客户，应通过正式的客户转移流程
2. **权限申请机制**：建立临时权限申请机制，用于特殊业务需求
3. **客户共享机制**：对于需要多人协作的客户，建立正式的共享机制

## 相关安全检查

### 已验证的安全点
- ✅ 后端API权限验证正确
- ✅ 前端API调用逻辑正确
- ✅ 数据库查询按员工ID正确过滤
- ✅ 角色权限验证机制正常

### 建议的后续安全检查
1. **审计日志**：添加客户查询的审计日志
2. **异常监控**：监控异常的客户查询行为
3. **权限测试**：定期进行权限渗透测试
4. **代码审查**：建立定期的安全代码审查机制

## 总结

本次修复成功消除了客户搜索功能中的严重安全漏洞，确保了数据的安全性和权限的合规性。修复后的系统严格按照"员工只能查看自己负责的客户"的业务规则执行，有效防止了数据泄露风险。

### 关键收获
1. **安全优先**：在设计搜索功能时，安全性应优先于便利性
2. **权限一致性**：所有数据访问路径都应遵循相同的权限规则
3. **代码审查重要性**：定期的安全代码审查能及时发现此类漏洞
4. **最小权限原则**：用户应只能访问其工作职责范围内的数据
