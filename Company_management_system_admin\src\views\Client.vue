<script setup>
import { ref, reactive, onMounted, computed } from 'vue';
import { ElMessage, ElMessageBox, ElNotification } from 'element-plus';
import {
    Search,
    Plus,
    Edit,
    Delete,
    RefreshRight,
    Check,
    Close,
} from '@element-plus/icons-vue';
import {
    getClientPage,
    addClient,
    updateClient,
    deleteClient,
    updateClientStatus,
    batchUpdateClientStatus,
    getPendingClients,
    getPendingClientCount
} from '@/api/client';
import { searchEmployeeByName, getEmployeesByDepartmentId, getEmployeeById, getEmployeePage } from '@/api/employee'; // 导入员工搜索API
import { getDepartmentList } from '@/api/department'; // 导入部门API

// 客户数据
const clientData = ref([]);
const loading = ref(true);
const searchText = ref('');
const searchEmployeeName = ref(''); // 用于存储输入的员工名称
const searchEmployeeId = ref(''); // 用于存储选中的员工ID
const employeeOptions = ref([]); // 搜索结果的员工列表
const employeeLoading = ref(false); // 员工搜索加载状态
const searchCategory = ref(''); // 用于存储选中的客户分类
const searchStatus = ref(''); // 用于存储选中的审批状态
const searchClientStatus = ref(''); // 用于存储选中的客户状态

// 客户审批相关
const approvalDialogVisible = ref(false);
const approvalLoading = ref(false);
const pendingClients = ref([]);
const selectedClients = ref([]);
const pendingCount = ref(0); // 待审批客户数量

// 部门相关
const departmentList = ref([]);
const loadingDepartments = ref(false);
const searchDepartmentId = ref(''); // 搜索区域选择的部门
const formDepartmentId = ref(''); // 表单中选择的部门
const formEmployeeOptions = ref([]); // 表单中的员工选项
const formEmployeeLoading = ref(false); // 表单中员工加载状态

// 分页设置
const pagination = reactive({
    currentPage: 1,
    pageSize: 10,
    total: 0,
});

// 对话框控制
const dialogVisible = ref(false);
const dialogType = ref('add'); // 'add' 或 'edit'
const formRef = ref(null);
const formLoading = ref(false);

// 表单数据
const form = reactive({
    client_id: null,
    name: '',
    contact_person: '',
    email: '',
    phone: '',
    remark: '',
    reject_remark: '', // 新增拒绝备注字段
    nationality: '',
    employee_id: null,
    category: '海运', // 默认选择海运
    status: '未审核', // 默认状态为未审核
    clientStatus: '报价中', // 客户状态
    operationTime: null, // 新增
});

// 表单规则
const rules = {
    name: [
        { required: true, message: '请输入客户名称', trigger: 'blur' },
        { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' },
    ],
    email: [
        { required: true, message: '请输入邮箱', trigger: 'blur' },
        { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' },
    ],
    employee_id: [
        { required: false, message: '请选择负责员工', trigger: 'change' },
    ],
    category: [
        { required: true, message: '请选择客户分类', trigger: 'change' },
    ],
    status: [
        { required: true, message: '请选择审批状态', trigger: 'change' },
    ],
    clientStatus: [
        { required: true, message: '请选择客户状态', trigger: 'change' },
    ],
};

// 加载部门列表
const loadDepartmentList = async () => {
    loadingDepartments.value = true;
    try {
        const res = await getDepartmentList();
        if (res.code === 200) {
            departmentList.value = res.data || [];
        } else {
            ElMessage.error(res.msg || '获取部门列表失败');
        }
    } catch (error) {
        ElMessage.error('加载部门列表失败: ' + (error.message || '未知错误'));
    } finally {
        loadingDepartments.value = false;
    }
};

// 处理部门变更
const handleDepartmentChange = async (departmentId, isFormChange = false) => {
    if (isFormChange) {
        formDepartmentId.value = departmentId;
        form.employee_id = null; // 清空已选员工
        formEmployeeOptions.value = []; // 清空员工选项
        // 不再自动加载部门所有员工
    } else {
        searchDepartmentId.value = departmentId;
        searchEmployeeId.value = ''; // 清空已选员工
        employeeOptions.value = []; // 清空员工选项
    }
};

// 加载部门员工
const loadDepartmentEmployees = async (departmentId, isForm = false) => {
    if (!departmentId) return;
    
    if (isForm) {
        formEmployeeLoading.value = true;
    } else {
        employeeLoading.value = true;
    }
    
    try {
        const res = await getEmployeesByDepartmentId(departmentId);
        if (res.code === 200) {
            if (isForm) {
                formEmployeeOptions.value = res.data || [];
            } else {
                employeeOptions.value = res.data || [];
            }
        } else {
            ElMessage.error(res.msg || '获取员工列表失败');
        }
    } catch (error) {
        ElMessage.error('加载员工列表失败: ' + (error.message || '未知错误'));
    } finally {
        if (isForm) {
            formEmployeeLoading.value = false;
        } else {
            employeeLoading.value = false;
        }
    }
};

// 远程搜索员工
const handleEmployeeRemoteSearch = async (query) => {
    if (!query) {
        // 如果没有查询内容，清空选项但不加载数据
        employeeOptions.value = [];
        return;
    }
    
    employeeLoading.value = true;
    try {
        // 使用员工名称搜索，不再依赖部门ID
        const res = await getEmployeePage({
            pageNum: 1,
            pageSize: 10,
            name: query
        });
        
        if (res.code === 200) {
            let employees = [];
            if (res.data && res.data.list) {
                employees = res.data.list;
            } else if (Array.isArray(res.data)) {
                employees = res.data;
            } else {
                employees = [];
            }

            // 为每个员工添加离职状态
            employees.forEach(emp => {
                emp.isResigned = !!(emp.exitDate);
            });

            employeeOptions.value = employees;
        } else {
            ElMessage.error(res.msg || '搜索员工失败');
        }
    } catch (error) {
        ElMessage.error('搜索员工失败: ' + (error.message || '未知错误'));
    } finally {
        employeeLoading.value = false;
    }
};

// 远程搜索表单中的员工
const handleFormEmployeeSearch = async (query) => {
    if (!formDepartmentId.value) {
        ElMessage.warning('请先选择部门');
        return;
    }
    
    if (!query) {
        // 如果没有查询内容，清空选项但不加载数据
        formEmployeeOptions.value = [];
        return;
    }
    
    formEmployeeLoading.value = true;
    try {
        // 使用部门ID和员工名称进行过滤搜索
        const res = await getEmployeePage({
            pageNum: 1,
            pageSize: 10,
            name: query,
            departmentId: formDepartmentId.value
        });
        
        if (res.code === 200) {
            let employees = [];
            if (res.data && res.data.list) {
                employees = res.data.list;
            } else if (Array.isArray(res.data)) {
                employees = res.data;
            } else {
                employees = [];
            }

            // 为每个员工添加离职状态
            employees.forEach(emp => {
                emp.isResigned = !!(emp.exitDate);
            });

            formEmployeeOptions.value = employees;
        } else {
            ElMessage.error(res.msg || '搜索员工失败');
        }
    } catch (error) {
        ElMessage.error('搜索员工失败: ' + (error.message || '未知错误'));
    } finally {
        formEmployeeLoading.value = false;
    }
};

// 加载客户数据
const loadClientData = async () => {
    loading.value = true;
    clientData.value = [];

    try {
        const res = await getClientPage({
            pageNum: pagination.currentPage,
            pageSize: pagination.pageSize,
            name: searchText.value || undefined,
            departmentId: searchDepartmentId.value || undefined, // 新增部门ID参数
            employeeName: searchEmployeeName.value || undefined, // 修改为员工名称参数
            category: searchCategory.value || undefined,
            status: searchStatus.value || undefined, // 添加审批状态参数
            clientStatus: searchClientStatus.value || undefined, // 添加客户状态参数
        });

        if (res.code === 200) {
            // 处理列表数据
            if (res.data && Array.isArray(res.data.list)) {
                clientData.value = res.data.list;
            } else if (res.data && Array.isArray(res.data)) {
                // 如果返回的直接是数组
                clientData.value = res.data;
            } else {
                clientData.value = [];
            }
            
            // 处理分页数据
            if (res.data) {
                pagination.total = res.data.total || 0;
                pagination.currentPage = res.data.pageNum || res.data.page || pagination.currentPage;
                pagination.pageSize = res.data.pageSize || res.data.limit || pagination.pageSize;
            }
        } else {
            ElMessage.error(res.msg || '获取客户数据失败');
        }
    } catch (error) {
        ElMessage.error('加载客户数据失败: ' + (error.message || '未知错误'));
    } finally {
        loading.value = false;
    }
};

// 重置表单
const resetForm = () => {
    if (formRef.value) {
        formRef.value.resetFields();
    }
    form.client_id = null;
    form.name = '';
    form.contact_person = '';
    form.email = '';
    form.phone = '';
    form.remark = '';
    form.reject_remark = '';
    form.nationality = '';
    form.employee_id = null;
    form.category = '海运'; // 重置分类为默认值
    form.status = '未审核'; // 重置状态为默认值
    form.clientStatus = '报价中'; // 重置客户状态为默认值
    form.operationTime = null; // 新增
    formDepartmentId.value = '';
    formEmployeeOptions.value = [];
};

// 关闭对话框
const closeDialog = () => {
    dialogVisible.value = false;
    resetForm();
};

// 打开添加对话框
const handleAdd = () => {
    dialogType.value = 'add';
    resetForm();
    dialogVisible.value = true;
};

// 仅加载单个员工信息
const loadSingleEmployee = async (employeeId) => {
    formEmployeeLoading.value = true;
    try {
        const res = await getEmployeeById(employeeId);
        if (res.code === 200 && res.data) {
            // 创建只包含当前员工的列表
            formEmployeeOptions.value = [res.data];
            return res.data.departmentId;
        }
    } catch (error) {
        console.error('获取员工信息失败:', error);
    } finally {
        formEmployeeLoading.value = false;
    }
    return null;
};

// 打开编辑对话框
const handleEdit = async (row) => {
    dialogType.value = 'edit';
    resetForm();
    
    // 填充表单数据
    form.client_id = row.clientId;
    form.name = row.name;
    form.contact_person = row.contactPerson || '';
    form.email = row.email;
    form.phone = row.phone;
    form.remark = row.remark || '';
    form.reject_remark = row.rejectRemark || '';
    form.nationality = row.nationality || '';
    form.employee_id = row.employeeId;
    form.category = row.category || '海运';
    form.status = row.status || '未审核';
    form.clientStatus = row.clientStatus || '报价中';
    if (row.operationTime) {
        const date = new Date(row.operationTime);
        // 检查日期是否有效
        if (!isNaN(date.getTime())) { 
            try {
                // 使用 sv-SE locale 配合时区转换，尝试获取 YYYY-MM-DD HH:mm:ss 格式
                let beijingDateTimeStr = date.toLocaleString('sv-SE', { 
                    timeZone: 'Asia/Shanghai',
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit',
                    hour: '2-digit',
                    minute: '2-digit',
                    second: '2-digit',
                    hour12: false 
                });
                // 移除可能存在的 T 分隔符
                 beijingDateTimeStr = beijingDateTimeStr.replace('T', ' ');
                 form.operationTime = beijingDateTimeStr;
            } catch (e) {
                console.error("Error formatting operationTime:", e);
                form.operationTime = null; // 格式化出错则设为 null
            }
        } else {
            form.operationTime = null; // 无效日期字符串则设为 null
        }
    } else {
        form.operationTime = null; // 原始值为 null 或空则设为 null
    }
    
    // 如果有employeeId，获取员工信息并设置对应的部门
    if (row.employeeId) {
        const departmentId = await loadSingleEmployee(row.employeeId);
        if (departmentId) {
            formDepartmentId.value = departmentId;
        }
    }
    
    dialogVisible.value = true;
};

// 确认删除
const handleDelete = (row) => {
    ElMessageBox.confirm(
        `确定要删除客户 "${row.name}" 吗？此操作不可恢复。`,
        '删除确认',
        {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
        }
    )
    .then(async () => {
        loading.value = true;
        try {
            const res = await deleteClient(row.clientId);
            if (res.code === 200) {
                ElMessage({
                    type: 'success',
                    message: '删除客户成功',
                    duration: 2000,
                });
                loadClientData();
            } else {
                ElMessage({
                    type: 'error',
                    message: res.msg || '删除失败',
                    duration: 3000,
                });
                loading.value = false;
            }
        } catch (error) {
            ElMessage({
                type: 'error',
                message: '删除失败: ' + (error.message || '未知错误'),
                duration: 3000,
            });
            loading.value = false;
        }
    })
    .catch(() => {
        // 取消删除，不做处理
    });
};

// 提交表单
const submitForm = async (formEl) => {
    if (!formEl) return;

    await formEl.validate(async (valid, fields) => {
        if (valid) {
            formLoading.value = true;
            try {
                // 准备提交的数据，转换为后端需要的格式
                const submitData = {
                    clientId: form.client_id,
                    name: form.name,
                    contactPerson: form.contact_person,
                    email: form.email,
                    phone: form.phone,
                    remark: form.remark,
                    rejectRemark: form.reject_remark,
                    nationality: form.nationality,
                    employeeId: form.employee_id || null,
                    category: form.category,
                    status: form.status,
                    clientStatus: form.clientStatus,
                    // Ensure empty string is sent as null for operationTime
                    operationTime: form.operationTime === '' ? null : form.operationTime,
                };

                let res;
                if (dialogType.value === 'add') {
                    // 添加客户
                    res = await addClient(submitData);
                } else {
                    // 更新客户
                    res = await updateClient(submitData);
                }

                if (res.code === 200) {
                    ElMessage({
                        type: 'success',
                        message:
                            dialogType.value === 'add'
                                ? '添加客户成功'
                                : '更新客户成功',
                        duration: 2000,
                    });
                    dialogVisible.value = false;
                    resetForm();
                    loadClientData();
                } else {
                    ElMessage({
                        type: 'error',
                        message:
                            res.msg ||
                            (dialogType.value === 'add'
                                ? '添加失败'
                                : '更新失败'),
                        duration: 3000,
                    });
                }
            } catch (error) {
                ElMessage({
                    type: 'error',
                    message: '提交失败: ' + (error.message || '未知错误'),
                    duration: 3000,
                });
            } finally {
                formLoading.value = false;
            }
        } else {
            // 构建缺失字段的错误信息
            const errorFields = [];
            for (const key in fields) {
                if (fields.hasOwnProperty(key)) {
                    const errorField = fields[key];
                    if (errorField && errorField.length > 0) {
                        errorFields.push(errorField[0].message);
                    }
                }
            }

            // 显示所有错误信息
            ElMessage({
                type: 'warning',
                message: '请完善表单信息：' + errorFields.join('；'),
                duration: 5000,
            });
            return false;
        }
    });
};

// 搜索
const handleSearch = () => {
    pagination.currentPage = 1;
    loadClientData();
};

// 刷新
const handleRefresh = () => {
    searchText.value = '';
    searchEmployeeName.value = '';
    searchEmployeeId.value = '';
    searchDepartmentId.value = ''; // 重置部门ID
    searchCategory.value = '';
    searchStatus.value = '';
    searchClientStatus.value = '';
    employeeOptions.value = [];
    pagination.currentPage = 1;
    loadClientData();
};

// 页码变化
const handleCurrentChange = (page) => {
    pagination.currentPage = page;
    loadClientData();
};

// 每页条数变化
const handleSizeChange = (size) => {
    pagination.pageSize = size;
    pagination.currentPage = 1;
    loadClientData();
};

// 根据客户分类设置不同的Tag样式
const getCategoryTagType = (category) => {
    switch (category) {
        case '海运':
            return 'primary';
        case '空运':
            return 'success';
        case '散货':
            return 'warning';
        case '快递':
            return 'info';
        default:
            return '';
    }
};

// 根据审批状态设置不同的Tag样式
const getStatusTagType = (status) => {
    switch (status) {
        case '未审核':
            return 'info';
        case '审核中':
            return 'warning';
        case '审核通过':
            return 'success';
        case '已拒绝':
            return 'danger';
        default:
            return '';
    }
};

// 根据客户状态设置不同的Tag样式
const getClientStatusTagType = (clientStatus) => {
    switch (clientStatus) {
        case '报价中':
            return 'warning';
        case '已合作':
            return 'success';
        default:
            return 'info';
    }
};

// 状态显示映射（将后端状态映射为前端显示文本）
const getDisplayStatus = (status) => {
    switch (status) {
        case '已合作':
            return '审核通过';
        default:
            return status || '----';
    }
};

// 格式化时间
const formatDateTime = (dateTimeString) => {
    if (!dateTimeString) return '';
    // 返回格式化后的日期时间
    const date = new Date(dateTimeString);
    return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
    }).replace(/\//g, '-');
};

// 获取待审批客户数量
const loadPendingClientCount = async () => {
    try {
        const res = await getPendingClientCount();
        if (res.code === 200) {
            pendingCount.value = res.data || 0;
            
            // 如果有待审批客户，显示通知
            if (pendingCount.value > 0) {
                ElNotification({
                    title: '客户审批提醒',
                    message: `您有 ${pendingCount.value} 个客户待审批`,
                    type: 'info',
                    duration: 5000,
                    position: 'top-right'
                });
            }
        }
    } catch (error) {
        console.error('获取待审批客户数量失败:', error);
    }
};

// 打开客户审批对话框
const openApprovalDialog = async () => {
    approvalDialogVisible.value = true;
    approvalLoading.value = true;
    selectedClients.value = [];
    
    try {
        // 获取待审批的客户列表（状态为"审核中"的客户）
        const res = await getPendingClients();
        if (res.code === 200) {
            pendingClients.value = res.data || [];
        } else {
            ElMessage.error(res.msg || '获取待审批客户列表失败');
            pendingClients.value = [];
        }
    } catch (error) {
        console.error('获取待审批客户列表失败:', error);
        ElMessage.error('获取待审批客户列表失败: ' + (error.message || '未知错误'));
        pendingClients.value = [];
    } finally {
        approvalLoading.value = false;
    }
};

// 审批对话框关闭时刷新待审批数量
const handleApprovalDialogClosed = () => {
    loadPendingClientCount();
};

// 处理表格选择变化
const handleSelectionChange = (selection) => {
    selectedClients.value = selection;
};

// 通过客户申请
const approveClient = (client) => {
    ElMessageBox.confirm(
        `确定要通过客户 "${client.name}" 的申请吗？`,
        '审批确认',
        {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'success',
        }
    )
    .then(async () => {
        approvalLoading.value = true;
        try {
            const res = await updateClientStatus({
                clientId: client.clientId,
                status: '审核通过'
            });
            
            if (res.code === 200) {
                ElMessage.success('已通过客户申请');
                
                // 从待审批列表中移除该客户
                pendingClients.value = pendingClients.value.filter(
                    item => item.clientId !== client.clientId
                );
                
                // 刷新客户列表
                loadClientData();
            } else {
                ElMessage.error(res.msg || '审批操作失败');
            }
        } catch (error) {
            console.error('审批操作失败:', error);
            ElMessage.error('审批操作失败: ' + (error.message || '未知错误'));
        } finally {
            approvalLoading.value = false;
        }
    })
    .catch(() => {
        // 用户取消操作，不做处理
    });
};

// 拒绝客户申请
const rejectClient = (client) => {
    ElMessageBox.confirm(
        `确定要拒绝客户 "${client.name}" 的申请吗？`,
        '审批确认',
        {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
        }
    )
    .then(() => {
        // 在确认拒绝后，提示输入拒绝理由
        ElMessageBox.prompt('请输入拒绝理由：', '拒绝客户: ' + client.name, {
            confirmButtonText: '提交拒绝',
            cancelButtonText: '取消输入',
            inputPlaceholder: '拒绝理由（可选）',
            inputType: 'textarea',
            // inputValidator: (value) => {
            //     if (!value) return '拒绝理由不能为空';
            //     return true;
            // },
        }).then(async ({ value }) => {
            approvalLoading.value = true;
            try {
                const res = await updateClientStatus({
                    clientId: client.clientId,
                    status: '已拒绝',
                    rejectRemark: value || '' // 如果用户没填，传空字符串
                });
                
                if (res.code === 200) {
                    ElMessage.success('已拒绝客户申请');
                    pendingClients.value = pendingClients.value.filter(
                        item => item.clientId !== client.clientId
                    );
                    loadClientData(); // 刷新客户列表
                    loadPendingClientCount(); // 更新待审批数量
                } else {
                    ElMessage.error(res.msg || '审批操作失败');
                }
            } catch (error) {
                console.error('审批操作失败:', error);
                ElMessage.error('审批操作失败: ' + (error.message || '未知错误'));
            } finally {
                approvalLoading.value = false;
            }
        }).catch(() => {
            ElMessage.info('已取消输入拒绝理由');
        });
    })
    .catch(() => {
        // 用户取消最外层拒绝确认，不做处理
        ElMessage.info('已取消拒绝操作');
    });
};

// 批量通过客户申请
const batchApprove = () => {
    if (selectedClients.value.length === 0) {
        ElMessage.warning('请先选择要审批的客户');
        return;
    }
    
    ElMessageBox.confirm(
        `确定要通过选中的 ${selectedClients.value.length} 个客户申请吗？`,
        '批量审批确认',
        {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'success',
        }
    )
    .then(async () => {
        approvalLoading.value = true;
        try {
            const clientIds = selectedClients.value.map(client => client.clientId);
            const res = await batchUpdateClientStatus({
                clientIds,
                status: '审核通过'
            });
            
            if (res.code === 200) {
                ElMessage.success(`已通过 ${clientIds.length} 个客户申请`);
                
                // 从待审批列表中移除这些客户
                pendingClients.value = pendingClients.value.filter(
                    item => !clientIds.includes(item.clientId)
                );
                
                // 清空选中的客户
                selectedClients.value = [];
                
                // 刷新客户列表
                loadClientData();
            } else {
                ElMessage.error(res.msg || '批量审批操作失败');
            }
        } catch (error) {
            console.error('批量审批操作失败:', error);
            ElMessage.error('批量审批操作失败: ' + (error.message || '未知错误'));
        } finally {
            approvalLoading.value = false;
        }
    })
    .catch(() => {
        // 用户取消操作，不做处理
    });
};

// 批量拒绝客户申请
const batchReject = () => {
    if (selectedClients.value.length === 0) {
        ElMessage.warning('请先选择要审批的客户');
        return;
    }
    
    ElMessageBox.confirm(
        `确定要拒绝选中的 ${selectedClients.value.length} 个客户申请吗？`,
        '批量审批确认',
        {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
        }
    )
    .then(() => {
        // 提示输入批量拒绝理由
        ElMessageBox.prompt('请输入批量拒绝理由（将应用于所有选定客户）：', '批量拒绝客户', {
            confirmButtonText: '提交拒绝',
            cancelButtonText: '取消输入',
            inputPlaceholder: '批量拒绝理由（可选）',
            inputType: 'textarea',
        }).then(async ({ value }) => {
            approvalLoading.value = true;
            try {
                const clientIds = selectedClients.value.map(client => client.clientId);
                const res = await batchUpdateClientStatus({
                    clientIds,
                    status: '已拒绝',
                    rejectRemark: value || '' // 如果用户没填，传空字符串
                });
                
                if (res.code === 200) {
                    ElMessage.success(`已拒绝 ${clientIds.length} 个客户申请`);
                    pendingClients.value = pendingClients.value.filter(
                        item => !clientIds.includes(item.clientId)
                    );
                    selectedClients.value = [];
                    loadClientData(); // 刷新客户列表
                    loadPendingClientCount(); // 更新待审批数量
                } else {
                    ElMessage.error(res.msg || '批量审批操作失败');
                }
            } catch (error) {
                console.error('批量审批操作失败:', error);
                ElMessage.error('批量审批操作失败: ' + (error.message || '未知错误'));
            } finally {
                approvalLoading.value = false;
            }
        }).catch(() => {
            ElMessage.info('已取消输入批量拒绝理由');
        });
    })
    .catch(() => {
        ElMessage.info('已取消批量拒绝操作');
    });
};

// 新增：国籍/地区选项
const nationalityOptions = ref([
    { value: '国内-同行', label: '国内-同行' },
    { value: '国外-同行', label: '国外-同行' },
    { value: '国内-直客', label: '国内-直客' },
    { value: '国外-直客', label: '国外-直客' },
]);

// 初始加载
onMounted(() => {
    loadDepartmentList();
    loadClientData();
    loadPendingClientCount(); // 加载待审批客户数量
});
</script>

<template>
    <div class="client-container">
        <!-- 头部搜索和操作栏 -->
        <div class="toolbar">
            <div class="search-box">
                <el-input
                    v-model="searchText"
                    placeholder="搜索客户名称"
                    clearable
                    @keyup.enter="handleSearch"
                    style="width: 200px"
                >
                    <template #prefix>
                        <el-icon>
                            <Search />
                        </el-icon>
                    </template>
                </el-input>
                <!-- 部门搜索下拉框 -->
                <el-select
                    v-model="searchDepartmentId"
                    placeholder="选择部门"
                    clearable
                    filterable
                    style="width: 180px;"
                    @change="handleDepartmentChange($event, false)" 
                    :loading="loadingDepartments"
                >
                    <el-option
                        v-for="item in departmentList"
                        :key="item.departmentId"
                        :label="item.departmentName"
                        :value="item.departmentId"
                    />
                </el-select>
                <!-- 员工搜索下拉框，不再依赖部门 -->
                <el-input
                    v-model="searchEmployeeName"
                    placeholder="输入负责员工名称搜索"
                    clearable
                    @keyup.enter="handleSearch" 
                    style="width: 180px;"
                />
                <!-- 客户分类搜索下拉框 -->
                <el-select
                    v-model="searchCategory"
                    placeholder="选择客户分类"
                    clearable
                    style="width: 140px"
                >
                    <el-option
                        v-for="item in ['海运', '空运', '散货', '快递']"
                        :key="item"
                        :label="item"
                        :value="item"
                    />
                </el-select>
                <!-- 客户状态搜索下拉框 -->
                <el-select
                    v-model="searchClientStatus"
                    placeholder="选择客户状态"
                    clearable
                    style="width: 140px"
                >
                    <el-option
                        v-for="item in ['报价中', '已合作']"
                        :key="item"
                        :label="item"
                        :value="item"
                    />
                </el-select>
                <!-- 审批状态搜索下拉框 -->
                <el-select
                    v-model="searchStatus"
                    placeholder="选择审批状态"
                    clearable
                    style="width: 140px"
                >
                    <el-option
                        v-for="item in ['未审核', '审核中', '审核通过', '已拒绝']"
                        :key="item"
                        :label="item"
                        :value="item"
                    />
                </el-select>
                <el-button
                    type="primary"
                    @click="handleSearch"
                >搜索</el-button>
                <el-button @click="handleRefresh">
                    <el-icon>
                        <RefreshRight />
                    </el-icon>重置
                </el-button>
            </div>
            <div class="action-box">
                <el-button
                    type="primary"
                    @click="handleAdd"
                    class="add-btn"
                >
                    <el-icon>
                        <Plus />
                    </el-icon>添加客户
                </el-button>
                <el-button
                    type="success"
                    @click="openApprovalDialog"
                    class="approval-btn"
                >
                    <el-icon>
                        <Check />
                    </el-icon>客户审批
                    <el-badge v-if="pendingCount > 0" :value="pendingCount" class="approval-badge" />
                </el-button>
            </div>
        </div>

        <!-- 表格 -->
        <el-table
            v-loading="loading"
            :data="clientData"
            border
            row-key="clientId"
            :max-height="'calc(100vh - 220px)'"
            class="custom-table"
        >
            <el-table-column
                type="index"
                width="60"
                align="center"
                label="序号"
                fixed
                class-name="index-column"
            />
            <el-table-column
                prop="name"
                label="客户名称"
                min-width="120"
                show-overflow-tooltip
            />
            <!-- 新增：联系人列 -->
            <el-table-column
                prop="contactPerson"
                label="联系人"
                min-width="100"
                show-overflow-tooltip
            >
                <template #default="scope">
                    {{ scope.row.contactPerson || '----' }}
                </template>
            </el-table-column>
            <el-table-column
                prop="email"
                label="邮箱"
                min-width="150"
                show-overflow-tooltip
            />
            <el-table-column
                prop="phone"
                label="电话"
                min-width="130"
                show-overflow-tooltip
            />
            <!-- 新增：国籍/地区列 -->
            <el-table-column prop="nationality" label="国籍/地区" min-width="100" show-overflow-tooltip>
                <template #default="scope">
                    {{ scope.row.nationality || '----' }}
                </template>
            </el-table-column>

            <!-- 部门 -->
            <el-table-column
                prop="departmentName"
                label="部门"
                min-width="120"
                show-overflow-tooltip
            >
                <template #default="{ row }">
                    {{ row.departmentName || '----' }}
                </template>
            </el-table-column>

            <!-- 新增：负责员工列 -->
            <el-table-column
                prop="employeeName"
                label="负责员工"
                min-width="100"
                show-overflow-tooltip
            >
                <template #default="{ row }">
                    {{ row.employeeName || '----' }}
                </template>
            </el-table-column>
            
            <!-- 客户分类列 -->
            <el-table-column
                prop="category"
                label="客户分类"
                min-width="100"
                show-overflow-tooltip
            >
                <template #default="{ row }">
                    <el-tag
                        :type="getCategoryTagType(row.category)"
                        effect="plain"
                    >
                        {{ row.category || '海运' }}
                    </el-tag>
                </template>
            </el-table-column>

            <!-- 客户状态列 -->
            <el-table-column
                prop="clientStatus"
                label="客户状态"
                min-width="100"
                show-overflow-tooltip
            >
                <template #default="{ row }">
                    <el-tag
                        :type="getClientStatusTagType(row.clientStatus)"
                        effect="plain"
                    >
                        {{ row.clientStatus || '报价中' }}
                    </el-tag>
                </template>
            </el-table-column>

            <!-- 审批状态列 -->
            <el-table-column
                prop="status"
                label="审批状态"
                min-width="100"
                show-overflow-tooltip
            >
                <template #default="{ row }">
                    <el-tooltip
                        class="item"
                        effect="dark"
                        :content="row.rejectRemark"
                        placement="top"
                        :disabled="!(row.status === '已拒绝' && row.rejectRemark && row.rejectRemark.trim() !== '')"
                    >
                        <el-tag
                            :type="getStatusTagType(row.status)"
                            effect="light"
                        >
                            {{ getDisplayStatus(row.status) }}
                        </el-tag>
                    </el-tooltip>
                </template>
            </el-table-column>
            
            <!-- 修改：操单时间列，数据源改为 operationTime，并移到创建时间之前 -->
            <el-table-column
                prop="operationTime" 
                label="操单时间"
                min-width="180"
                show-overflow-tooltip
            >
                <template #default="scope">
                    {{ formatDateTime(scope.row.operationTime) || '----' }}  
                </template>
            </el-table-column>

            <!-- 新增：备注列 (移动到此位置) -->
            <el-table-column
                prop="remark"
                label="备注"
                min-width="150"
                show-overflow-tooltip
            >
                <template #default="scope">
                    {{ scope.row.remark || '----' }}
                </template>
            </el-table-column>

            <el-table-column
                prop="createTime" 
                label="创建时间"
                min-width="180"
                show-overflow-tooltip
            >
                <template #default="scope">
                    {{ formatDateTime(scope.row.createTime) }}
                </template>
            </el-table-column>
            <el-table-column
                label="操作"
                width="120"
                fixed="right"
                align="center"
            >
                <template #default="{ row }">
                    <div class="operation-buttons">
                        <el-button
                            class="edit-btn"
                            @click="handleEdit(row)"
                            title="编辑"
                        >
                            <el-icon>
                                <Edit />
                            </el-icon>
                        </el-button>
                        <el-button
                            class="delete-btn"
                            @click="handleDelete(row)"
                            title="删除"
                        >
                            <el-icon>
                                <Delete />
                            </el-icon>
                        </el-button>
                    </div>
                </template>
            </el-table-column>
        </el-table>

        <!-- 分页器 -->
        <div class="pagination-container">
            <el-pagination
                background
                layout="total, sizes, prev, pager, next, jumper"
                :current-page="pagination.currentPage"
                :page-size="pagination.pageSize"
                :total="pagination.total"
                :page-sizes="[10, 20, 50, 100]"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
            />
        </div>

        <!-- 添加/编辑对话框 -->
        <el-dialog
            v-model="dialogVisible"
            :title="dialogType === 'add' ? '添加客户' : '编辑客户'"
            width="500px"
            destroy-on-close
            class="custom-dialog"
        >
            <el-form
                ref="formRef"
                :model="form"
                :rules="rules"
                label-position="left"
                label-width="100px"
                class="dialog-form"
            >
                <el-form-item
                    label="客户名称"
                    prop="name"
                    required
                >
                    <el-input
                        v-model="form.name"
                        placeholder="请输入客户名称"
                    />
                </el-form-item>
                <!-- 新增：联系人表单项 -->
                <el-form-item
                    label="联系人"
                    prop="contact_person"
                >
                    <el-input
                        v-model="form.contact_person"
                        placeholder="请输入联系人姓名"
                    />
                </el-form-item>
                <el-form-item
                    label="邮箱"
                    prop="email"
                    required
                >
                    <el-input
                        v-model="form.email"
                        placeholder="请输入邮箱地址"
                    />
                </el-form-item>
                <el-form-item
                    label="电话"
                    prop="phone"
                >
                    <el-input
                        v-model="form.phone"
                        placeholder="请输入电话号码"
                    />
                </el-form-item>
                <el-form-item label="国籍/地区" prop="nationality">
                    <el-select 
                        v-model="form.nationality" 
                        placeholder="请选择国籍/地区"
                        style="width: 100%"
                        clearable 
                    >
                        <el-option
                            v-for="option in nationalityOptions"
                            :key="option.value"
                            :label="option.label"
                            :value="option.value"
                        />
                    </el-select>
                </el-form-item>
                <el-form-item
                    label="所属部门"
                    required
                >
                    <el-select
                        v-model="formDepartmentId"
                        placeholder="请选择部门"
                        clearable
                        style="width: 100%"
                        @change="handleDepartmentChange($event, true)"
                        :loading="loadingDepartments"
                    >
                        <el-option
                            v-for="item in departmentList"
                            :key="item.departmentId"
                            :label="item.departmentName"
                            :value="item.departmentId"
                        />
                    </el-select>
                </el-form-item>
                <el-form-item
                    label="负责员工"
                    prop="employee_id"
                    required
                >
                    <el-select
                        v-model="form.employee_id"
                        clearable
                        filterable
                        remote
                        style="width: 100%"
                        :disabled="!formDepartmentId"
                        placeholder="输入员工名称进行搜索"
                        :remote-method="handleFormEmployeeSearch"
                        :loading="formEmployeeLoading"
                    >
                        <template #empty>
                            <p class="empty-text">{{ formDepartmentId ? '请输入员工名称搜索' : '请先选择部门' }}</p>
                        </template>
                        <el-option
                            v-for="item in formEmployeeOptions"
                            :key="item.employeeId"
                            :label="item.name + (item.positionName ? '（' + item.positionName + '）' : '') + (item.isResigned ? '（已离职）' : '')"
                            :value="item.employeeId"
                        />
                    </el-select>
                </el-form-item>
                <el-form-item
                    label="客户分类"
                    prop="category"
                >
                    <el-select
                        v-model="form.category"
                        placeholder="请选择客户分类"
                        clearable
                        style="width: 100%"
                    >
                        <el-option
                            v-for="item in ['海运', '空运', '散货', '快递']"
                            :key="item"
                            :label="item"
                            :value="item"
                        />
                    </el-select>
                </el-form-item>
                <el-form-item
                    label="客户状态"
                    prop="clientStatus"
                >
                    <el-select
                        v-model="form.clientStatus"
                        placeholder="请选择客户状态"
                        clearable
                        style="width: 100%"
                    >
                        <el-option
                            v-for="item in ['报价中', '已合作']"
                            :key="item"
                            :label="item"
                            :value="item"
                        />
                    </el-select>
                </el-form-item>
                <el-form-item
                    label="审批状态"
                    prop="status"
                >
                    <el-select
                        v-model="form.status"
                        placeholder="请选择审批状态"
                        clearable
                        style="width: 100%"
                    >
                        <el-option
                            v-for="item in ['未审核', '审核中', '审核通过', '已拒绝']"
                            :key="item"
                            :label="item"
                            :value="item"
                        />
                    </el-select>
                </el-form-item>
                <el-form-item label="操单时间" prop="operationTime">
                     <el-date-picker
                        v-model="form.operationTime"
                        type="datetime"
                        placeholder="选择操单时间"
                        style="width: 100%"
                        clearable
                        value-format="YYYY-MM-DD HH:mm:ss"
                    />
                </el-form-item>
                <!-- 新增：备注表单项 (移动到此正确位置) -->
                <el-form-item
                    label="备注"
                    prop="remark"
                >
                    <el-input
                        v-model="form.remark"
                        type="textarea"
                        placeholder="请输入备注信息"
                        :rows="3"
                    />
                </el-form-item>
                <!-- 新增：拒绝备注表单项 (仅当状态为已拒绝时显示) -->
                <el-form-item
                    v-if="form.status === '已拒绝'"
                    label="拒绝备注"
                    prop="reject_remark"
                >
                    <el-input
                        v-model="form.reject_remark"
                        type="textarea"
                        placeholder="请输入拒绝备注"
                        :rows="3"
                    />
                </el-form-item>
            </el-form>
            <div class="dialog-footer">
                <el-button @click="closeDialog">取消</el-button>
                <el-button
                    type="primary"
                    @click="submitForm(formRef)"
                    :loading="formLoading"
                >确定</el-button>
            </div>
        </el-dialog>

        <!-- 客户审批对话框 -->
        <el-dialog
            v-model="approvalDialogVisible"
            title="客户审批"
            width="90%"
            :style="{ maxWidth: '1200px' }"
            :close-on-click-modal="false"
            append-to-body
            @closed="handleApprovalDialogClosed"
        >
            <div class="action-toolbar">
                <el-button type="success" :icon="Check" @click="batchApprove" :disabled="selectedClients.length === 0">
                    批量通过
                </el-button>
                <el-button type="danger" :icon="Close" @click="batchReject" :disabled="selectedClients.length === 0">
                    批量拒绝
                </el-button>
            </div>

            <el-table
                v-loading="approvalLoading"
                :data="pendingClients"
                border
                class="custom-table approval-table"
                :max-height="'calc(70vh - 180px)'"
                style="width: 100%; margin-bottom: 0;"
                @selection-change="handleSelectionChange"
            >
                <el-table-column type="selection" width="50" align="center" />
                <el-table-column prop="name" label="客户名称" min-width="110" show-overflow-tooltip align="center" />
                <!-- 新增：联系人列 -->
                <el-table-column prop="contactPerson" label="联系人" min-width="90" show-overflow-tooltip align="center">
                    <template #default="scope">
                        {{ scope.row.contactPerson || '----' }}
                    </template>
                </el-table-column>
                <el-table-column prop="phone" label="联系电话" min-width="110" show-overflow-tooltip align="center" />
                <el-table-column prop="email" label="邮箱" min-width="130" show-overflow-tooltip align="center" />
                <el-table-column prop="nationality" label="国籍/地区" min-width="100" show-overflow-tooltip align="center">
                    <template #default="scope">
                        {{ scope.row.nationality || '----' }}
                    </template>
                </el-table-column>
                <el-table-column prop="departmentName" label="部门" min-width="110" show-overflow-tooltip align="center" />
                <el-table-column prop="employeeName" label="负责员工" min-width="90" show-overflow-tooltip align="center" />
                <el-table-column prop="category" label="客户类型" min-width="90" align="center">
                    <template #default="scope">
                        <el-tag :type="getCategoryTagType(scope.row.category)">
                            {{ scope.row.category }}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column prop="status" label="状态" min-width="90" align="center">
                    <template #default="scope">
                        <el-tag :type="getStatusTagType(scope.row.status)">
                            {{ getDisplayStatus(scope.row.status) }}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column prop="remark" label="备注" min-width="130" show-overflow-tooltip align="center">
                    <template #default="scope">
                        {{ scope.row.remark || '----' }}
                    </template>
                </el-table-column>
                <el-table-column label="操作" width="120" fixed="right" align="center">
                    <template #default="scope">
                        <div class="approval-action-buttons">
                            <el-button
                                type="success"
                                link
                                :icon="Check"
                                @click="approveClient(scope.row)"
                            >
                                通过
                            </el-button>
                            <el-button
                                type="danger"
                                link
                                :icon="Close"
                                @click="rejectClient(scope.row)"
                            >
                                拒绝
                            </el-button>
                        </div>
                    </template>
                </el-table-column>
            </el-table>
        </el-dialog>
    </div>
</template>

<style scoped>
.client-container {
    padding: 20px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    min-height: calc(100vh - 100px);
    position: relative;
}

.toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 10px;
    background-color: #f9f9f9;
    border-radius: 6px;
}

.search-box {
    display: flex;
    gap: 10px;
    align-items: center;
    flex-wrap: wrap;
}

.search-box .el-input {
    width: 220px;
}

.action-box {
    display: flex;
    gap: 10px;
}

.add-btn {
    background-color: #409eff;
    border-color: #409eff;
    color: #fff;
    padding: 8px 16px;
    transition: all 0.3s ease;
}

.add-btn:hover {
    background-color: #66b1ff;
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.el-button {
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.el-button:hover {
    transform: scale(1.05);
}

.el-button .el-icon {
    margin-right: 4px;
}

.pagination-container {
    position: absolute;
    bottom: 20px;
    right: 20px;
    background-color: #fff;
    padding: 10px 15px;
    border-radius: 6px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    z-index: 1;
}

:deep(.custom-table) {
    margin-bottom: 60px;
    border-radius: 6px;
    overflow: hidden;
}

:deep(.el-table__body-wrapper) {
    overflow-x: auto;
}

:deep(.el-table__header-wrapper) {
    overflow-x: hidden;
}

:deep(.el-table .cell) {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    text-align: center !important;
    display: flex;
    justify-content: center;
    align-items: center;
}

:deep(.index-column) {
    background-color: #f5f7fa;
}

:deep(.el-table__row) {
    transition: all 0.3s ease;
}

:deep(.el-table__row:hover) {
    background-color: #ecf5ff !important;
    transform: translateY(-2px);
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

:deep(.operation-column) {
    background-color: #f9f9f9;
}

.operation-buttons {
    display: flex;
    justify-content: center;
    gap: 8px;
}

.edit-btn,
.delete-btn {
    border: none;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.edit-btn {
    background-color: #e6f1fc;
    color: #409eff;
}

.edit-btn:hover {
    background-color: #409eff;
    color: white;
    transform: translateY(-2px);
}

.delete-btn {
    background-color: #ffebec;
    color: #f56c6c;
}

.delete-btn:hover {
    background-color: #f56c6c;
    color: white;
    transform: translateY(-2px);
}

.status-tag {
    padding: 4px 8px;
    border-radius: 4px;
    font-weight: bold;
    transition: all 0.3s ease;
}

:deep(.status-tag:hover) {
    transform: scale(1.1);
}

.custom-dialog {
    border-radius: 8px;
}

.dialog-form {
    padding: 0 20px;
    max-height: 60vh;
    overflow-y: auto;
}

.dialog-form::-webkit-scrollbar {
    width: 6px;
}

.dialog-form::-webkit-scrollbar-thumb {
    background-color: #c0c4cc;
    border-radius: 3px;
}

.dialog-form::-webkit-scrollbar-track {
    background-color: #f5f7fa;
}

.dialog-form :deep(.el-form-item__label) {
    font-weight: 500;
    color: #606266;
}

.dialog-footer {
    display: flex;
    justify-content: flex-end;
    margin-top: 30px;
    padding: 0 20px 10px 20px;
    gap: 10px;
}

:deep(.custom-dialog .el-dialog__header) {
    padding: 20px;
    border-bottom: 1px solid #ebeef5;
    margin-right: 0;
}

:deep(.custom-dialog .el-dialog__body) {
    padding: 30px 0;
}

:deep(.custom-dialog .el-dialog__footer) {
    display: none;
}

:deep(.custom-dialog .el-input__wrapper),
:deep(.custom-dialog .el-textarea__inner),
:deep(.custom-dialog .el-select) {
    box-shadow: 0 0 0 1px #dcdfe6 inset;
    border-radius: 4px;
    transition: all 0.3s;
}

:deep(.custom-dialog .el-input__wrapper:hover),
:deep(.custom-dialog .el-textarea__inner:hover),
:deep(.custom-dialog .el-select:hover) {
    box-shadow: 0 0 0 1px #409eff inset;
}

:deep(.custom-dialog .el-input__wrapper.is-focus),
:deep(.custom-dialog .el-textarea__inner:focus),
:deep(.custom-dialog .el-select.is-focus) {
    box-shadow: 0 0 0 1px #409eff inset;
}

:deep(.inactive-row) {
    color: #c0c4cc;
    background-color: #f9f9f9;
}

/* 响应式调整 */
@media screen and (max-width: 768px) {
    .toolbar {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .search-box {
        width: 100%;
        flex-wrap: wrap;
    }

    .action-box {
        width: 100%;
        justify-content: flex-end;
    }
}

/* 添加空状态样式 */
.empty-text {
    text-align: center;
    color: #909399;
    font-size: 14px;
    padding: 10px 0;
}

.approval-btn {
    position: relative;
}

.approval-badge {
    position: absolute;
    top: -8px;
    right: -8px;
}

/* 添加审批操作工具栏样式 */
.action-toolbar {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-bottom: 15px;
    padding: 10px;
    background-color: #f9f9f9;
    border-radius: 6px;
}

/* 审批弹窗表格样式 */
:deep(.approval-table .el-table__cell) {
    text-align: center !important;
}

:deep(.approval-table .cell) {
    padding: 8px 5px;
    justify-content: center;
}

/* 审批操作按钮样式 */
.approval-action-buttons {
    display: flex;
    justify-content: center;
    gap: 0;
    flex-wrap: nowrap;
}

.approval-action-buttons .el-button {
    padding: 4px 6px;
    min-width: 52px;
    font-size: 12px;
}

.approval-action-buttons .el-button .el-icon {
    margin-right: 2px;
    font-size: 12px;
}
</style> 