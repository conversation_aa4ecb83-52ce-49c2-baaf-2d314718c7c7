<script setup>
import { ref, reactive, onMounted, computed } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import {
    Search,
    Plus,
    Edit,
    Delete,
    RefreshRight,
    Calendar,
    Loading,
    Upload,
} from '@element-plus/icons-vue';
import {
    getPerformancePage,
    addPerformance,
    updatePerformance,
    deletePerformance,
    getPerformanceById,
    batchDeletePerformance,
    importPerformanceExcel,
} from '@/api/performance';
import { getDepartmentList } from '@/api/department';
import { getEmployeeList, getEmployeesByDepartmentId, getEmployeeById, getEmployeePage } from '@/api/employee';
import ExcelImportDialog from '@/components/ExcelImportDialog.vue';
import ImportErrorsDialog from '@/components/ImportErrorsDialog.vue';

// 业绩数据
const performanceData = ref([]);
const loading = ref(true);
const searchEmployeeName = ref('');
const searchDepartmentId = ref('');
const searchYearMonth = ref('');

// 部门列表和员工列表
const departmentList = ref([]);
const employeeList = ref([]);
const loadingDepartments = ref(false);
const loadingEmployees = ref(false);
const formDepartmentId = ref(''); // 用于部门选择框的绑定值

// 选中的记录
const selectedPerformances = ref([]);

// 分页设置
const pagination = reactive({
    page: 1,
    size: 10,
    total: 0,
});

// 对话框控制
const dialogVisible = ref(false);
const dialogType = ref('add'); // 'add' 或 'edit'
const formRef = ref(null);
const formLoading = ref(false);

// 表单数据
const form = reactive({
    id: null,
    employeeId: null,
    employeeName: '',
    departmentId: null,
    department: '',
    position: '',
    date: '',
    estimatedPerformance: 0,
    actualPerformance: 0,
});

// 表单规则
const rules = {
    departmentId: [
        { required: true, message: '请选择部门', trigger: 'change' },
    ],
    employeeId: [{ required: true, message: '请选择员工', trigger: 'change' }],
    date: [{ required: true, message: '请选择年月', trigger: 'change' }],
    estimatedPerformance: [
        { required: true, message: '请输入预估业绩', trigger: 'blur' },
        { type: 'number', message: '预估业绩必须为数字', trigger: 'blur' },
    ],
    actualPerformance: [
        { required: true, message: '请输入实际业绩', trigger: 'blur' },
        { type: 'number', message: '实际业绩必须为数字', trigger: 'blur' },
    ],
};

// 加载部门列表
const loadDepartmentList = async () => {
    loadingDepartments.value = true;
    try {
        const res = await getDepartmentList();
        if (res.code === 200) {
            departmentList.value = res.data;
        } else {
            ElMessage.error(res.message || '获取部门列表失败');
        }
    } catch (error) {
        ElMessage.error('加载部门列表失败: ' + (error.message || '未知错误'));
    } finally {
        loadingDepartments.value = false;
    }
};

// 处理部门变更
const handleDepartmentChange = (departmentId) => {
    // 当部门变更时，清空员工选择
    form.employeeId = '';
    form.position = '';
    
    // 同步设置 form.departmentId
    form.departmentId = departmentId;
    
    // 不再自动加载部门所有员工
};

// 远程搜索员工
const handleEmployeeRemoteSearch = async (query) => {
    if (!formDepartmentId.value) {
        ElMessage.warning('请先选择部门');
        return;
    }
    
    if (!query) {
        // 如果没有查询内容，清空选项但不加载数据
        employeeList.value = [];
        return;
    }
    
    // 确保部门列表已加载
    if (departmentList.value.length === 0) {
        await loadDepartmentList();
    }
    
    loadingEmployees.value = true;
    try {
        // 使用部门ID和员工名称进行过滤搜索
        const res = await getEmployeePage({
            pageNum: 1,
            pageSize: 10,
            name: query,
            departmentId: formDepartmentId.value
        });
        
        if (res.code === 200) {
            let employees = [];
            if (res.data && res.data.list) {
                employees = res.data.list;
            } else if (Array.isArray(res.data)) {
                employees = res.data;
            }
            
            // 为每个员工添加部门名称和离职状态
            employees.forEach(emp => {
                if (emp.departmentId) {
                    const dept = departmentList.value.find(d =>
                        d.departmentId === emp.departmentId ||
                        d.departmentId === parseInt(emp.departmentId, 10));
                    emp.departmentName = dept ? dept.departmentName : '未知部门';
                } else {
                    emp.departmentName = '未知部门';
                }

                // 检查是否已离职
                emp.isResigned = !!(emp.exitDate);
            });
            
            employeeList.value = employees;
        } else {
            ElMessage.error(res.message || '搜索员工失败');
        }
    } catch (error) {
        ElMessage.error('搜索员工失败: ' + (error.message || '未知错误'));
    } finally {
        loadingEmployees.value = false;
    }
};

// 处理员工变更
const handleEmployeeChange = (employeeId) => {
    if (!employeeId) {
        form.position = '';
        return;
    }

    // 查找选中的员工信息
    const selectedEmployee = employeeList.value.find(
        (emp) => emp.employeeId === employeeId
    );
    if (selectedEmployee) {
        // 自动填充职位
        form.position = selectedEmployee.positionName || '';
    }
};

// 加载业绩数据
const loadPerformanceData = async () => {
    loading.value = true;
    performanceData.value = [];

    try {
        const params = {
            page: pagination.page,
            size: pagination.size,
            employeeName: searchEmployeeName.value || undefined,
            departmentId: searchDepartmentId.value || undefined,
            yearMonth: searchYearMonth.value || undefined,
        };

        const res = await getPerformancePage(params);

        if (res.code === 200) {
            performanceData.value = res.data.records || [];
            pagination.total = res.data.total || 0;
        } else {
            ElMessage.error(res.message || '获取业绩数据失败');
        }
    } catch (error) {
        ElMessage.error('加载业绩数据失败: ' + (error.message || '未知错误'));
    } finally {
        loading.value = false;
    }
};

// 重置表单
const resetForm = () => {
    if (formRef.value) {
        formRef.value.resetFields();
    }
    form.id = null;
    form.employeeId = null;
    form.employeeName = '';
    form.departmentId = null;
    form.department = '';
    form.position = '';
    form.date = '';
    form.estimatedPerformance = 0;
    form.actualPerformance = 0;
    
    // 重置部门选择框绑定值
    formDepartmentId.value = '';
};

// 关闭对话框
const closeDialog = () => {
    dialogVisible.value = false;
    resetForm();
};

// 打开添加对话框
const handleAdd = () => {
    dialogType.value = 'add';
    resetForm();
    dialogVisible.value = true;
};

// 打开编辑对话框
const handleEdit = async (row) => {
    dialogType.value = 'edit';
    resetForm();

    try {
        // 确保部门列表已加载，只在列表为空时加载
        if (departmentList.value.length === 0) {
            await loadDepartmentList();
        }
        
        // 用后端数据填充表单
        const res = await getPerformanceById(row.id);
        if (res.code === 200) {
            const data = res.data;
            
            // 先设置其他属性
            form.id = data.id;
            form.employeeId = data.employeeId;
            form.employeeName = data.employeeName;
            form.department = data.department; // 保存部门名称
            form.position = data.position;
            form.date = data.date;
            form.estimatedPerformance = data.estimatedPerformance;
            form.actualPerformance = data.actualPerformance;

            // 根据部门名称查找部门ID
            let departmentId = null;
            if (data.department) {
                const foundDepartment = departmentList.value.find(
                    dept => dept.departmentName === data.department
                );
                if (foundDepartment) {
                    departmentId = foundDepartment.departmentId;
                }
            }
            
            // 如果API返回了departmentId，则优先使用
            if (data.departmentId) {
                departmentId = typeof data.departmentId === 'string' 
                    ? parseInt(data.departmentId, 10) 
                    : data.departmentId;
            }
                
            // 设置部门ID
            form.departmentId = departmentId;
            formDepartmentId.value = departmentId;
            
            // 如果没有找到部门ID，重新加载部门列表
            if (!departmentId && data.department) {
                console.warn(`部门 "${data.department}" 在部门列表中不存在，正在重新加载部门列表...`);
                await loadDepartmentList();
                
                // 重新尝试查找部门
                const deptAfterReload = departmentList.value.find(
                    dept => dept.departmentName === data.department
                );
                if (deptAfterReload) {
                    departmentId = deptAfterReload.departmentId;
                    form.departmentId = departmentId;
                    formDepartmentId.value = departmentId;
                }
            }
            
            // 直接使用业绩数据构造员工对象，无需再调用API
            if (data.employeeId) {
                // 构造与远程搜索结果格式一致的员工对象
                employeeList.value = [{
                    employeeId: data.employeeId,
                    name: data.employeeName,
                    positionName: data.position,
                    departmentId: departmentId
                }];
            }

            // 对话框显示
            dialogVisible.value = true;
        } else {
            ElMessage.error(res.message || '获取业绩详情失败');
        }
    } catch (error) {
        ElMessage.error('获取业绩详情失败: ' + (error.message || '未知错误'));
    }
};

// 确认删除业绩记录
const handleDelete = (row) => {
    ElMessageBox.confirm(
        `确定要删除员工 "${row.employeeName}" 在 "${row.date}" 的业绩记录吗？此操作不可撤销！`,
        '删除确认',
        {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
        }
    )
        .then(async () => {
            loading.value = true;
            try {
                const res = await deletePerformance(row.id);

                if (res.code === 200) {
                    ElMessage({
                        type: 'success',
                        message: '删除成功',
                        duration: 2000,
                    });
                    loadPerformanceData();
                } else {
                    ElMessage.error(res.message || '删除失败');
                    loading.value = false;
                }
            } catch (error) {
                ElMessage.error('删除失败: ' + (error.message || '未知错误'));
                loading.value = false;
            }
        })
        .catch(() => {
            // 取消删除，不做处理
        });
};

// 批量删除
const handleBatchDelete = () => {
    if (selectedPerformances.value.length === 0) {
        ElMessage.warning('请选择要删除的记录');
        return;
    }

    const ids = selectedPerformances.value.map((item) => item.id);
    const names = selectedPerformances.value
        .map((item) => `${item.employeeName}(${item.date})`)
        .join('、');

    ElMessageBox.confirm(
        `确定要删除以下员工的业绩记录吗？\n${names}\n此操作不可撤销！`,
        '批量删除确认',
        {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
        }
    )
        .then(async () => {
            loading.value = true;
            try {
                const res = await batchDeletePerformance(ids);

                if (res.code === 200) {
                    ElMessage({
                        type: 'success',
                        message: '批量删除成功',
                        duration: 2000,
                    });
                    selectedPerformances.value = [];
                    loadPerformanceData();
                } else {
                    ElMessage.error(res.message || '批量删除失败');
                    loading.value = false;
                }
            } catch (error) {
                ElMessage.error(
                    '批量删除失败: ' + (error.message || '未知错误')
                );
                loading.value = false;
            }
        })
        .catch(() => {
            // 取消删除，不做处理
        });
};

// 提交表单
const submitForm = async (formEl) => {
    if (!formEl) return;

    await formEl.validate(async (valid) => {
        if (valid) {
            formLoading.value = true;
            try {
                // 准备提交的数据
                const submitData = {
                    id: form.id, // 编辑时需要ID
                    employeeId: form.employeeId,
                    date: form.date,
                    estimatedPerformance: form.estimatedPerformance,
                    actualPerformance: form.actualPerformance,
                };

                let res;
                if (dialogType.value === 'add') {
                    // 新增业绩记录
                    res = await addPerformance(submitData);
                } else {
                    // 更新业绩记录
                    res = await updatePerformance(submitData);
                }

                if (res.code === 200) {
                    ElMessage({
                        type: 'success',
                        message:
                            dialogType.value === 'add'
                                ? '添加成功'
                                : '更新成功',
                        duration: 2000,
                    });
                    dialogVisible.value = false;
                    resetForm();
                    loadPerformanceData();
                } else {
                    ElMessage.error(
                        res.message ||
                            (dialogType.value === 'add'
                                ? '添加失败'
                                : '更新失败')
                    );
                }
            } catch (error) {
                ElMessage.error('提交失败: ' + (error.message || '未知错误'));
            } finally {
                formLoading.value = false;
            }
        } else {
            ElMessage.warning('请完善表单信息');
            return false;
        }
    });
};

// 表格选中行变化
const handleSelectionChange = (selection) => {
    selectedPerformances.value = selection;
};

// 搜索
const handleSearch = () => {
    pagination.page = 1;
    loadPerformanceData();
};

// 重置搜索
const handleReset = () => {
    searchEmployeeName.value = '';
    searchDepartmentId.value = '';
    searchYearMonth.value = '';
    pagination.page = 1;
    loadPerformanceData();
};

// 页码变化
const handleCurrentChange = (page) => {
    pagination.page = page;
    loadPerformanceData();
};

// 每页条数变化
const handleSizeChange = (size) => {
    pagination.size = size;
    pagination.page = 1;
    loadPerformanceData();
};

// 格式化金额
const formatCurrency = (value) => {
    if (value === null || value === undefined) return '¥0.00';
    return (
        '¥' +
        parseFloat(value)
            .toFixed(2)
            .replace(/\B(?=(\d{3})+(?!\d))/g, ',')
    );
};

// 获取部门名称
const getDepartmentName = (departmentId) => {
    const department = departmentList.value.find(
        (item) => item.departmentId === departmentId
    );
    return department ? department.departmentName : '';
};

// 导入Excel对话框控制
const showImportDialog = ref(false);
const showImportErrorsDialog = ref(false);
const currentImportResult = ref(null);

// 打开导入对话框
const handleOpenImportDialog = () => {
    showImportDialog.value = true;
};

// 处理Excel导入成功事件 (替代旧的 handleImportSubmit)
const handlePerformanceImportSuccess = (importResult, importType) => {
    // Ensure this handler is specific to performance if needed, or make it generic
    if (importType === 'performance') { 
        currentImportResult.value = importResult;
        loading.value = true; // Keep loading state or manage it based on importResult
        try {
            if (importResult && (importResult.failureCount > 0 || (importResult.generalErrors && importResult.generalErrors.length > 0))) {
                // Message is already shown by ExcelImportDialog, just open error details
                showImportErrorsDialog.value = true;
            } else {
                // Message is already shown by ExcelImportDialog
                // ElMessage.success('业绩数据导入成功'); // Already handled by component
                showImportDialog.value = false; // Close import dialog if open, though it closes itself
            }
            loadPerformanceData(); // Refresh data regardless of partial success or full success
        } catch (error) {
            ElMessage.error('处理导入结果时发生错误: ' + (error.message || '未知错误'));
            currentImportResult.value = null;
        } finally {
            loading.value = false;
        }
    }
};

// Formatter functions for performance import messages
const formatPerformanceSuccessMsg = (importResult) => {
    return `业绩数据全部导入成功！共处理 ${importResult.processedRows || 0} 行，成功导入 ${importResult.successCount || 0} 条记录。`;
};

const formatPerformancePartialSuccessMsg = (importResult) => {
    let msg = `业绩数据导入处理完成。共处理 ${importResult.processedRows || 0} 行，成功 ${importResult.successCount || 0} 行，失败/跳过 ${importResult.failureCount || 0} 行。`;
    if (importResult.generalErrors && importResult.generalErrors.length > 0) {
        msg += ` 通用错误: ${importResult.generalErrors.join('; ')}`;
    }
    return msg;
};

// 初始加载
onMounted(() => {
    loadDepartmentList();
    loadPerformanceData();
});
</script>

<template>
    <div class="performance-container">
        <!-- 搜索工具栏 -->
        <div class="toolbar">
            <div class="search-box">
                <el-input
                    v-model="searchEmployeeName"
                    placeholder="搜索员工姓名"
                    clearable
                    @keyup.enter="handleSearch"
                >
                    <template #prefix>
                        <el-icon>
                            <Search />
                        </el-icon>
                    </template>
                </el-input>

                <el-select
                    v-model="searchDepartmentId"
                    placeholder="选择部门"
                    clearable
                    :loading="loadingDepartments"
                >
                    <el-option
                        v-for="item in departmentList"
                        :key="item.departmentId"
                        :label="item.departmentName"
                        :value="item.departmentId"
                    />
                </el-select>

                <el-date-picker
                    v-model="searchYearMonth"
                    type="month"
                    placeholder="选择年月"
                    format="YYYY-MM"
                    value-format="YYYY-MM"
                />

                <el-button
                    type="primary"
                    @click="handleSearch"
                >
                    <el-icon>
                        <Search />
                    </el-icon>搜索
                </el-button>

                <el-button @click="handleReset">
                    <el-icon>
                        <RefreshRight />
                    </el-icon>重置
                </el-button>
            </div>

            <div class="action-box">
                <el-button
                    type="danger"
                    :disabled="selectedPerformances.length === 0"
                    @click="handleBatchDelete"
                >
                    <el-icon>
                        <Delete />
                    </el-icon>批量删除
                </el-button>

                <el-button
                    type="primary"
                    class="add-btn"
                    @click="handleAdd"
                >
                    <el-icon>
                        <Plus />
                    </el-icon>添加业绩
                </el-button>

                <el-button
                type="success"
                @click="handleOpenImportDialog"
            >
                <el-icon>
                    <Upload />
                </el-icon>导入业绩
            </el-button>
            </div>
        </div>

        <!-- 业绩表格 -->
        <el-table
            v-loading="loading"
            :data="performanceData"
            border
            row-key="id"
            @selection-change="handleSelectionChange"
            :max-height="'calc(100vh - 220px)'"
            class="custom-table"
        >
            <el-table-column
                type="selection"
                width="55"
                align="center"
            />

            <el-table-column
                label="员工姓名"
                prop="employeeName"
                min-width="100"
                show-overflow-tooltip
            />

            <el-table-column
                label="部门"
                prop="department"
                min-width="120"
                show-overflow-tooltip
            />

            <el-table-column
                label="职位"
                prop="position"
                min-width="120"
                show-overflow-tooltip
            />

            <el-table-column
                label="年月"
                prop="date"
                width="100"
                align="center"
            >
                <template #default="{ row }">
                    <el-tag type="info">{{ row.date }}</el-tag>
                </template>
            </el-table-column>

            <el-table-column
                label="预估业绩"
                prop="estimatedPerformance"
                min-width="150"
                align="right"
            >
                <template #default="{ row }">
                    {{ formatCurrency(row.estimatedPerformance) }}
                </template>
            </el-table-column>

            <el-table-column
                label="实际业绩"
                prop="actualPerformance"
                min-width="150"
                align="right"
            >
                <template #default="{ row }">
                    {{ formatCurrency(row.actualPerformance) }}
                </template>
            </el-table-column>

            <el-table-column
                label="本月备用金"
                prop="totalPettyCash"
                min-width="150"
                align="right"
            >
                <template #default="{ row }">
                    <span v-if="row.totalPettyCash !== null && row.totalPettyCash !== undefined">
                        {{ formatCurrency(row.totalPettyCash) }}
                    </span>
                    <el-tag
                        v-else
                        type="info"
                        size="small"
                    >暂无数据</el-tag>
                </template>
            </el-table-column>

            <el-table-column
                label="本月发布工资"
                prop="totalSalary"
                min-width="150"
                align="right"
            >
                <template #default="{ row }">
                    <span v-if="row.totalSalary !== null && row.totalSalary !== undefined">
                        {{ formatCurrency(row.totalSalary) }}
                    </span>
                    <el-tag
                        v-else
                        type="info"
                        size="small"
                    >暂无数据</el-tag>
                </template>
            </el-table-column>

            <el-table-column
                label="本月平均部门开销"
                prop="averageDepartmentExpense"
                min-width="170"
                align="right"
            >
                <template #default="{ row }">
                    <span v-if="row.averageDepartmentExpense !== null && row.averageDepartmentExpense !== undefined">
                        {{ formatCurrency(row.averageDepartmentExpense) }}
                    </span>
                    <el-tag
                        v-else
                        type="info"
                        size="small"
                    >暂无数据</el-tag>
                </template>
            </el-table-column>

            <el-table-column
                label="本月员工费用"
                prop="totalEmployeeOtherExpenses"
                min-width="160"
                align="right"
            >
                <template #default="{ row }">
                    <span v-if="row.totalEmployeeOtherExpenses !== null && row.totalEmployeeOtherExpenses !== undefined">
                        {{ formatCurrency(row.totalEmployeeOtherExpenses) }}
                    </span>
                    <el-tag
                        v-else
                        type="info"
                        size="small"
                    >暂无数据</el-tag>
                </template>
            </el-table-column>

            <el-table-column
                label="本月预计盈亏"
                prop="estimatedMonthlyProfitLoss"
                min-width="160"
                align="right"
            >
                <template #default="{ row }">
                    <span v-if="row.estimatedMonthlyProfitLoss !== null && row.estimatedMonthlyProfitLoss !== undefined">
                        {{ formatCurrency(row.estimatedMonthlyProfitLoss) }}
                    </span>
                    <el-tag
                        v-else
                        type="info"
                        size="small"
                    >暂无数据</el-tag>
                </template>
            </el-table-column>

            <el-table-column
                label="本月实际盈亏"
                prop="actualMonthlyProfitLoss"
                min-width="160"
                align="right"
            >
                <template #default="{ row }">
                    <span v-if="row.actualMonthlyProfitLoss !== null && row.actualMonthlyProfitLoss !== undefined">
                        {{ formatCurrency(row.actualMonthlyProfitLoss) }}
                    </span>
                    <el-tag
                        v-else
                        type="info"
                        size="small"
                    >暂无数据</el-tag>
                </template>
            </el-table-column>

            <el-table-column
                label="操作"
                width="150"
                align="center"
                fixed="right"
                class-name="operation-column"
            >
                <template #default="{ row }">
                    <div class="operation-buttons">
                        <el-button
                            class="edit-btn"
                            @click="handleEdit(row)"
                            title="编辑"
                        >
                            <el-icon>
                                <Edit />
                            </el-icon>
                        </el-button>
                        <el-button
                            class="delete-btn"
                            @click="handleDelete(row)"
                            title="删除"
                        >
                            <el-icon>
                                <Delete />
                            </el-icon>
                        </el-button>
                    </div>
                </template>
            </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-container">
            <el-pagination
                background
                layout="total, sizes, prev, pager, next, jumper"
                :current-page="pagination.page"
                :page-size="pagination.size"
                :total="pagination.total"
                :page-sizes="[10, 20, 50, 100]"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
            />
        </div>

        <!-- 添加/编辑对话框 -->
        <el-dialog
            v-model="dialogVisible"
            :title="dialogType === 'add' ? '添加业绩记录' : '编辑业绩记录'"
            width="500px"
            destroy-on-close
            class="custom-dialog"
        >
            <el-form
                ref="formRef"
                :model="form"
                :rules="rules"
                label-width="100px"
                class="dialog-form"
            >
                <el-form-item
                    label="部门"
                    prop="departmentId"
                    required
                >
                    <el-select
                        v-model="formDepartmentId"
                        placeholder="选择部门"
                        :loading="loadingDepartments"
                        @change="handleDepartmentChange"
                    >
                        <el-option
                            v-for="item in departmentList"
                            :key="item.departmentId"
                            :label="item.departmentName"
                            :value="item.departmentId"
                        />
                    </el-select>
                </el-form-item>

                <el-form-item
                    label="员工"
                    prop="employeeId"
                    required
                >
                    <el-select
                        v-model="form.employeeId"
                        placeholder="请先选择部门再输入员工名称搜索"
                        :disabled="!formDepartmentId"
                        @change="handleEmployeeChange"
                        remote
                        filterable
                        :remote-method="handleEmployeeRemoteSearch"
                        :loading="loadingEmployees"
                        style="width: 100%"
                    >
                        <template #empty>
                            <p class="empty-text">{{ formDepartmentId ? '请输入员工名称搜索' : '请先选择部门' }}</p>
                        </template>
                        <el-option
                            v-for="employee in employeeList"
                            :key="employee.employeeId"
                            :label="employee.name + (employee.departmentName ? ` (${employee.departmentName}${employee.positionName ? '-' + employee.positionName : ''})` : (employee.positionName ? ` (${employee.positionName})` : '')) + (employee.isResigned ? '（已离职）' : '')"
                            :value="employee.employeeId"
                        />
                    </el-select>
                    <div
                        v-if="!formDepartmentId && dialogType === 'add'"
                        class="el-form-item__help"
                    >
                    </div>
                </el-form-item>

                <el-form-item
                    label="年月"
                    prop="date"
                    required
                >
                    <el-date-picker
                        v-model="form.date"
                        type="month"
                        placeholder="选择年月"
                        format="YYYY-MM"
                        value-format="YYYY-MM"
                        style="width: 100%"
                    />
                </el-form-item>

                <el-form-item
                    label="预估业绩"
                    prop="estimatedPerformance"
                    required
                >
                    <el-input-number
                        v-model="form.estimatedPerformance"
                        :precision="2"
                        :step="1000"
                        style="width: 100%"
                    />
                </el-form-item>

                <el-form-item
                    label="实际业绩"
                    prop="actualPerformance"
                    required
                >
                    <el-input-number
                        v-model="form.actualPerformance"
                        :precision="2"
                        :step="1000"
                        style="width: 100%"
                    />
                </el-form-item>
            </el-form>

            <div class="dialog-footer">
                <el-button @click="closeDialog">取消</el-button>
                <el-button
                    type="primary"
                    :loading="formLoading"
                    @click="submitForm(formRef)"
                >确定</el-button>
            </div>
        </el-dialog>

        <!-- Excel导入对话框 -->
        <ExcelImportDialog
            v-if="showImportDialog" 
            :model-value="showImportDialog"
            @update:modelValue="showImportDialog = $event"
            importType="performance" 
            dialogTitle="导入业绩数据"
            templateFileName="业绩导入模板.xlsx" 
            uploadUrl="/api/performance/import" 
            :maxFileSizeMB="200" 
            @import-success="handlePerformanceImportSuccess"
            :successMessageFormatter="formatPerformanceSuccessMsg"
            :partialSuccessMessageFormatter="formatPerformancePartialSuccessMsg"
        />

        <!-- 导入错误详情对话框 -->
        <ImportErrorsDialog 
            v-model="showImportErrorsDialog" 
            :import-result="currentImportResult" 
            title="业绩导入结果"
        />
    </div>
</template>

<style scoped>
.performance-container {
    padding: 20px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    min-height: calc(100vh - 100px);
    position: relative;
}

.toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 10px;
    background-color: #f9f9f9;
    border-radius: 6px;
}

.search-box {
    display: flex;
    gap: 10px;
    align-items: center;
    flex-wrap: wrap;
}

.search-box .el-input,
.search-box .el-select,
.search-box .el-date-picker {
    width: 220px;
}

.action-box {
    display: flex;
    gap: 10px;
}

.add-btn {
    background-color: #409eff;
    border-color: #409eff;
    color: #fff;
    padding: 8px 16px;
    transition: all 0.3s ease;
}

.add-btn:hover {
    background-color: #66b1ff;
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.el-button {
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.el-button:hover {
    transform: scale(1.05);
}

.el-button .el-icon {
    margin-right: 4px;
}

.pagination-container {
    position: absolute;
    bottom: 20px;
    right: 20px;
    background-color: #fff;
    padding: 10px 15px;
    border-radius: 6px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    z-index: 1;
}

:deep(.custom-table) {
    margin-bottom: 60px;
    border-radius: 6px;
    overflow: hidden;
}

:deep(.el-table__body-wrapper) {
    overflow-x: auto;
}

:deep(.el-table__header-wrapper) {
    overflow-x: hidden;
}

:deep(.el-table .cell) {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    text-align: center;
}

:deep(.index-column) {
    background-color: #f5f7fa;
}

:deep(.el-table__row) {
    transition: all 0.3s ease;
}

:deep(.el-table__row:hover) {
    background-color: #ecf5ff !important;
    transform: translateY(-2px);
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

:deep(.operation-column) {
    background-color: #f9f9f9;
}

.operation-buttons {
    display: flex;
    justify-content: center;
    gap: 8px;
}

.edit-btn,
.delete-btn {
    border: none;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.edit-btn {
    background-color: #e6f1fc;
    color: #409eff;
}

.edit-btn:hover {
    background-color: #409eff;
    color: white;
    transform: translateY(-2px);
}

.delete-btn {
    background-color: #ffebec;
    color: #f56c6c;
}

.delete-btn:hover {
    background-color: #f56c6c;
    color: white;
    transform: translateY(-2px);
}

.custom-dialog {
    border-radius: 8px;
}

.dialog-form {
    padding: 0 20px;
    max-height: 60vh;
    overflow-y: auto;
}

.dialog-form::-webkit-scrollbar {
    width: 6px;
}

.dialog-form::-webkit-scrollbar-thumb {
    background-color: #c0c4cc;
    border-radius: 3px;
}

.dialog-form::-webkit-scrollbar-track {
    background-color: #f5f7fa;
}

.dialog-form :deep(.el-form-item__label) {
    font-weight: 500;
    color: #606266;
}

.dialog-footer {
    display: flex;
    justify-content: flex-end;
    margin-top: 30px;
    padding: 0 20px 10px 20px;
    gap: 10px;
}

:deep(.custom-dialog .el-dialog__header) {
    padding: 20px;
    border-bottom: 1px solid #ebeef5;
    margin-right: 0;
}

:deep(.custom-dialog .el-dialog__body) {
    padding: 30px 0;
}

:deep(.custom-dialog .el-dialog__footer) {
    display: none;
}

.progress-column {
    padding-right: 10px;
}

:deep(.el-progress) {
    margin: 0 auto;
}

:deep(.custom-dialog .el-input__wrapper),
:deep(.custom-dialog .el-textarea__inner),
:deep(.custom-dialog .el-select) {
    box-shadow: 0 0 0 1px #dcdfe6 inset;
    border-radius: 4px;
    transition: all 0.3s;
}

:deep(.custom-dialog .el-input__wrapper:hover),
:deep(.custom-dialog .el-textarea__inner:hover),
:deep(.custom-dialog .el-select:hover) {
    box-shadow: 0 0 0 1px #409eff inset;
}

:deep(.custom-dialog .el-input__wrapper.is-focus),
:deep(.custom-dialog .el-textarea__inner:focus),
:deep(.custom-dialog .el-select.is-focus) {
    box-shadow: 0 0 0 1px #409eff inset;
}

.completion-rate-text {
    font-size: 12px;
    color: #67c23a;
    font-weight: bold;
    margin-top: 4px;
}

/* 添加总工资样式 */
:deep(.el-tag.el-tag--info) {
    color: #909399;
    background-color: #f4f4f5;
    border-color: #e9e9eb;
}

.loading-text {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 5px;
    color: #909399;
    font-size: 14px;
}

.loading-text .el-icon {
    animation: rotating 2s linear infinite;
}

@keyframes rotating {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}
</style> 